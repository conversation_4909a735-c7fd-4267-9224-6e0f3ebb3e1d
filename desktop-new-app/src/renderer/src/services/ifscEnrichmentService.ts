import { validate, fetchDetails } from 'ifsc'

// Define the IFSC details interface based on the ifsc package
interface IFSCDetails {
  BANK: string
  IFSC: string
  BRANCH: string
  ADDRESS: string
  CONTACT: string
  CITY: string
  RTGS: boolean
  NEFT: boolean
  IMPS: boolean
  UPI: boolean
  DISTRICT: string
  STATE: string
  BANKCODE: string
  MICR?: string
}

// Enhanced bank data structure
interface EnrichedBankData {
  original_bank_name: string
  bank_name: string
  branch_name: string
  branch_address: string
  branch_state: string
  branch_city: string
  branch_district: string
  ifsc_code: string
  is_ifsc_valid: boolean
  enrichment_status: 'success' | 'invalid_ifsc' | 'api_error' | 'no_ifsc'
  enriched_at: string
}

// Transaction data interface (matching the backend structure)
interface TransactionData {
  receiver_ifsc?: string
  receiver_bank?: string
  [key: string]: any
}

class IFSCEnrichmentService {
  private cache: Map<string, IFSCDetails | null> = new Map()
  private readonly maxCacheSize = 1000
  private readonly cacheExpiryMs = 24 * 60 * 60 * 1000 // 24 hours

  /**
   * Validate an IFSC code
   */
  validateIFSC(ifscCode: string): boolean {
    try {
      if (!ifscCode || typeof ifscCode !== 'string') {
        return false
      }
      
      // Clean the IFSC code
      const cleanIFSC = ifscCode.trim().toUpperCase()
      
      // Basic format validation (4 letters + 7 characters)
      if (!/^[A-Z]{4}[A-Z0-9]{7}$/.test(cleanIFSC)) {
        return false
      }
      
      // Use the ifsc package validation
      return validate(cleanIFSC)
    } catch (error) {
      console.error('Error validating IFSC:', error)
      return false
    }
  }

  /**
   * Fetch IFSC details with caching
   */
  async fetchIFSCDetails(ifscCode: string): Promise<IFSCDetails | null> {
    try {
      if (!ifscCode) return null
      
      const cleanIFSC = ifscCode.trim().toUpperCase()
      
      // Check cache first
      if (this.cache.has(cleanIFSC)) {
        return this.cache.get(cleanIFSC) || null
      }
      
      // Validate before fetching
      if (!this.validateIFSC(cleanIFSC)) {
        this.cache.set(cleanIFSC, null)
        return null
      }
      
      // Fetch from API
      const details = await fetchDetails(cleanIFSC)
      
      // Cache the result
      this.cacheResult(cleanIFSC, details)
      
      return details
    } catch (error) {
      console.error(`Error fetching IFSC details for ${ifscCode}:`, error)
      // Cache null result to avoid repeated API calls for invalid codes
      this.cacheResult(ifscCode.trim().toUpperCase(), null)
      return null
    }
  }

  /**
   * Cache management
   */
  private cacheResult(ifscCode: string, details: IFSCDetails | null): void {
    // Simple LRU cache implementation
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    
    this.cache.set(ifscCode, details)
  }

  /**
   * Enrich bank data for a single transaction
   */
  async enrichTransactionBankData(transaction: TransactionData): Promise<TransactionData> {
    try {
      const receiverIFSC = transaction.receiver_ifsc
      const originalBankName = transaction.receiver_bank || ''
      
      // Create enriched bank data object
      const enrichedBankData: EnrichedBankData = {
        original_bank_name: originalBankName,
        bank_name: originalBankName,
        branch_name: '',
        branch_address: '',
        branch_state: '',
        branch_city: '',
        branch_district: '',
        ifsc_code: receiverIFSC || '',
        is_ifsc_valid: false,
        enrichment_status: 'no_ifsc',
        enriched_at: new Date().toISOString()
      }
      
      if (!receiverIFSC) {
        // No IFSC code available
        enrichedBankData.enrichment_status = 'no_ifsc'
      } else {
        // Validate IFSC
        const isValid = this.validateIFSC(receiverIFSC)
        enrichedBankData.is_ifsc_valid = isValid
        
        if (!isValid) {
          enrichedBankData.enrichment_status = 'invalid_ifsc'
        } else {
          // Fetch IFSC details
          const ifscDetails = await this.fetchIFSCDetails(receiverIFSC)
          
          if (ifscDetails) {
            // Update with enriched data
            enrichedBankData.bank_name = ifscDetails.BANK || originalBankName
            enrichedBankData.branch_name = ifscDetails.BRANCH || ''
            enrichedBankData.branch_address = ifscDetails.ADDRESS || ''
            enrichedBankData.branch_state = ifscDetails.STATE || ''
            enrichedBankData.branch_city = ifscDetails.CITY || ''
            enrichedBankData.branch_district = ifscDetails.DISTRICT || ''
            enrichedBankData.enrichment_status = 'success'
            
            // Replace the receiver_bank field with standardized bank name
            transaction.receiver_bank = ifscDetails.BANK
          } else {
            enrichedBankData.enrichment_status = 'api_error'
          }
        }
      }
      
      // Add enriched bank data to transaction
      transaction.enriched_bank_data = enrichedBankData
      
      return transaction
    } catch (error) {
      console.error('Error enriching transaction bank data:', error)
      
      // Add error information to transaction
      transaction.enriched_bank_data = {
        original_bank_name: transaction.receiver_bank || '',
        bank_name: transaction.receiver_bank || '',
        branch_name: '',
        branch_address: '',
        branch_state: '',
        branch_city: '',
        branch_district: '',
        ifsc_code: transaction.receiver_ifsc || '',
        is_ifsc_valid: false,
        enrichment_status: 'api_error',
        enriched_at: new Date().toISOString()
      }
      
      return transaction
    }
  }

  /**
   * Enrich bank data for multiple transactions
   */
  async enrichTransactionsBankData(transactions: TransactionData[]): Promise<TransactionData[]> {
    const enrichedTransactions: TransactionData[] = []
    
    // Process transactions in batches to avoid overwhelming the API
    const batchSize = 10
    for (let i = 0; i < transactions.length; i += batchSize) {
      const batch = transactions.slice(i, i + batchSize)
      
      // Process batch concurrently
      const enrichedBatch = await Promise.all(
        batch.map(transaction => this.enrichTransactionBankData(transaction))
      )
      
      enrichedTransactions.push(...enrichedBatch)
      
      // Add small delay between batches to be respectful to the API
      if (i + batchSize < transactions.length) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }
    
    return enrichedTransactions
  }

  /**
   * Get enrichment statistics
   */
  getEnrichmentStats(transactions: TransactionData[]): {
    total: number
    enriched: number
    invalid_ifsc: number
    no_ifsc: number
    api_errors: number
  } {
    const stats = {
      total: transactions.length,
      enriched: 0,
      invalid_ifsc: 0,
      no_ifsc: 0,
      api_errors: 0
    }
    
    transactions.forEach(transaction => {
      const enrichedData = transaction.enriched_bank_data as EnrichedBankData
      if (enrichedData) {
        switch (enrichedData.enrichment_status) {
          case 'success':
            stats.enriched++
            break
          case 'invalid_ifsc':
            stats.invalid_ifsc++
            break
          case 'no_ifsc':
            stats.no_ifsc++
            break
          case 'api_error':
            stats.api_errors++
            break
        }
      }
    })
    
    return stats
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear()
  }
}

// Export singleton instance
export const ifscEnrichmentService = new IFSCEnrichmentService()
export type { EnrichedBankData, TransactionData }
