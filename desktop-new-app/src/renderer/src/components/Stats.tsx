import React, { useMemo, useState, useEffect } from 'react'
import { Card } from './ui/Card'
import { Button } from './ui/Button'
import { ifscEnrichmentService, EnrichedBankData, TransactionData } from '../services/ifscEnrichmentService'

interface StatsProps {
  transactions: TransactionData[]
  loading?: boolean
}

interface GeographicStats {
  state: string
  city: string
  district: string
  totalAmount: number
  transactionCount: number
  banks: Set<string>
  avgAmount: number
}

interface BankStats {
  bankName: string
  totalAmount: number
  transactionCount: number
  states: Set<string>
  cities: Set<string>
  avgAmount: number
}

const Stats: React.FC<StatsProps> = ({ transactions, loading = false }) => {
  const [enrichedTransactions, setEnrichedTransactions] = useState<TransactionData[]>([])
  const [enriching, setEnriching] = useState(false)
  const [enrichmentStats, setEnrichmentStats] = useState<any>(null)

  // Enrich transactions with IFSC data
  const handleEnrichData = async () => {
    if (transactions.length === 0) return

    setEnriching(true)
    try {
      const enriched = await ifscEnrichmentService.enrichTransactionsBankData([...transactions])
      setEnrichedTransactions(enriched)
      
      const stats = ifscEnrichmentService.getEnrichmentStats(enriched)
      setEnrichmentStats(stats)
    } catch (error) {
      console.error('Error enriching transaction data:', error)
    } finally {
      setEnriching(false)
    }
  }

  // Auto-enrich when transactions change
  useEffect(() => {
    if (transactions.length > 0 && enrichedTransactions.length === 0) {
      handleEnrichData()
    }
  }, [transactions])

  // Calculate geographic statistics
  const geographicStats = useMemo(() => {
    const statsMap = new Map<string, GeographicStats>()

    enrichedTransactions.forEach(transaction => {
      const enrichedData = transaction.enriched_bank_data as EnrichedBankData
      if (!enrichedData || enrichedData.enrichment_status !== 'success') return

      const amount = parseFloat(transaction.amount || '0')
      if (isNaN(amount) || amount <= 0) return

      const state = enrichedData.branch_state || 'Unknown'
      const city = enrichedData.branch_city || 'Unknown'
      const district = enrichedData.branch_district || 'Unknown'
      const bank = enrichedData.bank_name || 'Unknown'

      const key = `${state}-${city}-${district}`
      
      if (!statsMap.has(key)) {
        statsMap.set(key, {
          state,
          city,
          district,
          totalAmount: 0,
          transactionCount: 0,
          banks: new Set(),
          avgAmount: 0
        })
      }

      const stats = statsMap.get(key)!
      stats.totalAmount += amount
      stats.transactionCount += 1
      stats.banks.add(bank)
      stats.avgAmount = stats.totalAmount / stats.transactionCount
    })

    return Array.from(statsMap.values()).sort((a, b) => b.totalAmount - a.totalAmount)
  }, [enrichedTransactions])

  // Calculate bank statistics
  const bankStats = useMemo(() => {
    const statsMap = new Map<string, BankStats>()

    enrichedTransactions.forEach(transaction => {
      const enrichedData = transaction.enriched_bank_data as EnrichedBankData
      if (!enrichedData || enrichedData.enrichment_status !== 'success') return

      const amount = parseFloat(transaction.amount || '0')
      if (isNaN(amount) || amount <= 0) return

      const bankName = enrichedData.bank_name || 'Unknown'
      const state = enrichedData.branch_state || 'Unknown'
      const city = enrichedData.branch_city || 'Unknown'

      if (!statsMap.has(bankName)) {
        statsMap.set(bankName, {
          bankName,
          totalAmount: 0,
          transactionCount: 0,
          states: new Set(),
          cities: new Set(),
          avgAmount: 0
        })
      }

      const stats = statsMap.get(bankName)!
      stats.totalAmount += amount
      stats.transactionCount += 1
      stats.states.add(state)
      stats.cities.add(city)
      stats.avgAmount = stats.totalAmount / stats.transactionCount
    })

    return Array.from(statsMap.values()).sort((a, b) => b.totalAmount - a.totalAmount)
  }, [enrichedTransactions])

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="text-center">Loading transaction data...</div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Geographic Fraud Analysis</h2>
        <Button 
          onClick={handleEnrichData} 
          disabled={enriching || transactions.length === 0}
          className="bg-blue-600 hover:bg-blue-700"
        >
          {enriching ? 'Enriching Data...' : 'Refresh IFSC Data'}
        </Button>
      </div>

      {/* Enrichment Status */}
      {enrichmentStats && (
        <Card className="p-4">
          <h3 className="text-lg font-semibold mb-3">Data Enrichment Status</h3>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{enrichmentStats.total}</div>
              <div className="text-gray-600">Total Transactions</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{enrichmentStats.enriched}</div>
              <div className="text-gray-600">Successfully Enriched</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{enrichmentStats.no_ifsc}</div>
              <div className="text-gray-600">No IFSC Code</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{enrichmentStats.invalid_ifsc}</div>
              <div className="text-gray-600">Invalid IFSC</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">{enrichmentStats.api_errors}</div>
              <div className="text-gray-600">API Errors</div>
            </div>
          </div>
        </Card>
      )}

      {/* Geographic Hotspots */}
      <Card className="p-4">
        <h3 className="text-lg font-semibold mb-4">Fraud Hotspots by Location</h3>
        {geographicStats.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            No geographic data available. Enrich transaction data with IFSC information to see hotspots.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">State</th>
                  <th className="text-left p-2">City</th>
                  <th className="text-left p-2">District</th>
                  <th className="text-right p-2">Total Amount</th>
                  <th className="text-right p-2">Transactions</th>
                  <th className="text-right p-2">Avg Amount</th>
                  <th className="text-right p-2">Banks Involved</th>
                </tr>
              </thead>
              <tbody>
                {geographicStats.slice(0, 10).map((stat, index) => (
                  <tr key={`${stat.state}-${stat.city}-${stat.district}`} className="border-b hover:bg-gray-50">
                    <td className="p-2 font-medium">{stat.state}</td>
                    <td className="p-2">{stat.city}</td>
                    <td className="p-2">{stat.district}</td>
                    <td className="p-2 text-right font-semibold text-red-600">
                      {formatCurrency(stat.totalAmount)}
                    </td>
                    <td className="p-2 text-right">{stat.transactionCount}</td>
                    <td className="p-2 text-right">{formatCurrency(stat.avgAmount)}</td>
                    <td className="p-2 text-right">{stat.banks.size}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </Card>

      {/* Bank Analysis */}
      <Card className="p-4">
        <h3 className="text-lg font-semibold mb-4">Bank-wise Fraud Analysis</h3>
        {bankStats.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            No bank data available. Enrich transaction data with IFSC information to see bank analysis.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Bank Name</th>
                  <th className="text-right p-2">Total Amount</th>
                  <th className="text-right p-2">Transactions</th>
                  <th className="text-right p-2">Avg Amount</th>
                  <th className="text-right p-2">States</th>
                  <th className="text-right p-2">Cities</th>
                </tr>
              </thead>
              <tbody>
                {bankStats.slice(0, 10).map((stat, index) => (
                  <tr key={stat.bankName} className="border-b hover:bg-gray-50">
                    <td className="p-2 font-medium">{stat.bankName}</td>
                    <td className="p-2 text-right font-semibold text-red-600">
                      {formatCurrency(stat.totalAmount)}
                    </td>
                    <td className="p-2 text-right">{stat.transactionCount}</td>
                    <td className="p-2 text-right">{formatCurrency(stat.avgAmount)}</td>
                    <td className="p-2 text-right">{stat.states.size}</td>
                    <td className="p-2 text-right">{stat.cities.size}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </Card>
    </div>
  )
}

export default Stats
