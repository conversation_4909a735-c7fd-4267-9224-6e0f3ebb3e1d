import React from 'react'
import { useThemeContext } from '../context/useThemeContext'

interface LampBackgroundProps {
  children: React.ReactNode
}

const LampBackground: React.FC<LampBackgroundProps> = ({ children }) => {
  const { isDark } = useThemeContext()

  return (
    <div className="min-h-screen relative overflow-hidden bg-background">
      {/* Lamp lighting effect from top */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Main lamp light cone */}
        <div 
          className={`absolute top-0 left-1/2 transform -translate-x-1/2 w-96 h-96 ${
            isDark 
              ? 'bg-gradient-radial from-blue-500/20 via-blue-400/10 to-transparent' 
              : 'bg-gradient-radial from-yellow-300/30 via-yellow-200/15 to-transparent'
          } rounded-full blur-3xl`}
        />
        
        {/* Secondary ambient light */}
        <div 
          className={`absolute top-0 left-1/2 transform -translate-x-1/2 w-[600px] h-[600px] ${
            isDark 
              ? 'bg-gradient-radial from-indigo-500/10 via-purple-400/5 to-transparent' 
              : 'bg-gradient-radial from-orange-200/20 via-yellow-100/10 to-transparent'
          } rounded-full blur-[100px]`}
        />
        
        {/* Subtle side lighting */}
        <div 
          className={`absolute top-20 left-0 w-64 h-64 ${
            isDark 
              ? 'bg-gradient-radial from-cyan-500/15 to-transparent' 
              : 'bg-gradient-radial from-blue-200/20 to-transparent'
          } rounded-full blur-2xl`}
        />
        <div 
          className={`absolute top-20 right-0 w-64 h-64 ${
            isDark 
              ? 'bg-gradient-radial from-purple-500/15 to-transparent' 
              : 'bg-gradient-radial from-pink-200/20 to-transparent'
          } rounded-full blur-2xl`}
        />
      </div>
      
      {/* Content with glassmorphic effect */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  )
}

export default LampBackground
